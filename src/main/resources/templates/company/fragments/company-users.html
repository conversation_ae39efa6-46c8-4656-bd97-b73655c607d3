<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Company Users Fragment</title>
</head>
<body>

<div th:fragment="companyUsers(companyId, companyName)">
    <!-- Company Users Content -->
    <div class="company-users-container">
        <!-- Header with company name -->
        <div class="mb-3">
            <h6 class="fw-semibold" th:text="${companyName}">Company Name</h6>
        </div>

        <!-- Search and Actions Bar -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <!-- Search Box -->
            <div class="input-group input-group-merge">
                <span class="input-group-text"><i class="ti ti-search"></i></span>
                <input type="text" 
                       class="form-control" 
                       id="userSearchInput" 
                       name="userSearchInput"
                       placeholder="Search users..." 
                       hx-post="/admin/companies/users/search"
                       hx-trigger="keyup changed delay:1000ms"
                       hx-target="#companyUsersList"
                       hx-include="[name='companyId'], [name='userSearchInput']"
                       hx-indicator="#searchSpinner">
                <span class="input-group-text" id="searchSpinner" style="display: none;">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                </span>
                <button type="button" 
                        class="btn btn-outline-secondary input-group-text"
                        onclick="clearUserSearch()"
                        title="Clear filter">
                    <i class="ti ti-x"></i>
                </button>
            </div>

            <!-- Add User Button -->
            <button type="button" 
                    class="btn btn-primary btn-sm" 
                    hx-get="/admin/companies/users/available?page=1&size=10"
                    hx-target="#availableUsersModal .modal-body"
                    hx-include="[name='companyId']"
                    hx-indicator="#modalHeaderSpinner"
                    data-bs-toggle="modal"
                    data-bs-target="#availableUsersModal">
                <i class="ti ti-plus me-1"></i>
            </button>
        </div>

        <!-- Hidden Company ID -->
        <input type="hidden" name="companyId" th:value="${companyId}">

        <!-- Selected Users Actions (initially hidden) -->
        <div id="selectedUsersActions" class="d-none mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted"><span id="selectedUsersCount">0</span> users selected</span>
                <div class="dropdown">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ti ti-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item text-danger" 
                               href="javascript:void(0);" 
                               hx-delete="/admin/companies/users/remove"
                               hx-include="[name='companyId'], [name='selectedUsers']"
                               hx-confirm="Are you sure you want to remove the selected users from this company?"
                               hx-target="#companyUsersList">
                                <i class="ti ti-trash me-1"></i> Remove from Company
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Users List Container - This div contains both the list and pagination -->
        <div id="companyUsersList" class="mt-3">
            <!-- The actual users list -->
            <div th:if="${users == null || users.totalElements == 0}" class="text-center text-muted py-4">
                <i class="ti ti-users ti-lg mb-2"></i>
                <div>No users found for this company</div>
            </div>

            <div th:if="${users != null && users.totalElements > 0}" class="list-group mb-3">
                <div th:each="user : ${users.content}" class="list-group-item list-group-item-action d-flex align-items-center">
                    <div class="form-check me-3">
                        <input class="form-check-input user-select-checkbox" 
                               type="checkbox" 
                               th:value="${user.id}" 
                               name="selectedUsers"
                               onchange="toggleUserSelection(this)">
                    </div>
                    <div class="d-flex align-items-center flex-grow-1">
                        <div class="avatar me-3">
                            <img th:if="${user.photo != null}" th:src="${'/images/' + user.photo}" class="rounded-circle" alt="User Photo">
                            <span th:if="${user.photo == null}" class="avatar-initial rounded-circle bg-label-primary">
                                <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                            </span>
                        </div>
                        <div class="d-flex flex-column">
                            <h6 class="mb-0" th:text="${user.fullName}">User Name</h6>
                            <small class="text-muted" th:text="${user.email}"><EMAIL></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div th:if="${users != null && users.totalElements > 0}" class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    <span>Showing from</span>
                    <span th:text="${users.number * users.size + 1}">1</span>
                    <span>to</span>
                    <span th:text="${users.number * users.size + users.numberOfElements}">10</span>
                    <span>of total</span>
                    <span th:text="${users.totalElements}">100</span>
                    <span>users</span>
                </div>

                <nav aria-label="User pagination" th:if="${users.totalPages > 1}">
                    <ul class="pagination pagination-rounded pagination-sm mb-0">
                        <!-- First Page Button -->
                        <li class="paginate_button page-item first" th:classappend="${users.first} ? 'disabled' : ''">
                            <a href="#"
                               class="page-link"
                               th:hx-get="@{/admin/companies/{id}/users(id=${companyId}, page=1, size=10)}"
                               hx-target="#companyUsersList"
                               hx-swap="innerHTML"
                               hx-indicator="#modalHeaderSpinner"
                               hx-headers='{"HX-Request": "true"}'>
                                <i class="ti ti-chevrons-left ti-xs"></i>
                            </a>
                        </li>

                        <!-- Previous Button -->
                        <li class="paginate_button page-item previous" th:classappend="${!users.hasPrevious()} ? 'disabled' : ''">
                            <a href="#"
                               class="page-link"
                               th:hx-get="@{/admin/companies/{id}/users(id=${companyId}, page=${users.number}, size=10)}"
                               hx-target="#companyUsersList"
                               hx-swap="innerHTML"
                               hx-indicator="#modalHeaderSpinner"
                               hx-headers='{"HX-Request": "true"}'>
                                <i class="ti ti-chevron-left ti-xs"></i>
                            </a>
                        </li>

                        <!-- Page Numbers -->
                        <th:block th:if="${users.totalPages <= 5}">
                            <th:block th:each="i : ${#numbers.sequence(1, users.totalPages)}">
                                <li class="paginate_button page-item" th:classappend="${i == currentPage} ? 'active' : ''">
                                    <a href="#"
                                       class="page-link"
                                       th:text="${i}"
                                       th:hx-get="@{/admin/companies/{id}/users(id=${companyId}, page=${i}, size=10)}"
                                       hx-target="#companyUsersList"
                                       hx-swap="innerHTML"
                                       hx-indicator="#modalHeaderSpinner"
                                       hx-headers='{"HX-Request": "true"}'>
                                    </a>
                                </li>
                            </th:block>
                        </th:block>

                        <!-- Next Button -->
                        <li class="paginate_button page-item next" th:classappend="${!users.hasNext()} ? 'disabled' : ''">
                            <a href="#"
                               class="page-link"
                               th:hx-get="@{/admin/companies/{id}/users(id=${companyId}, page=${users.number + 2}, size=10)}"
                               hx-target="#companyUsersList"
                               hx-swap="innerHTML"
                               hx-indicator="#modalHeaderSpinner"
                               hx-headers='{"HX-Request": "true"}'>
                                <i class="ti ti-chevron-right ti-xs"></i>
                            </a>
                        </li>

                        <!-- Last Page Button -->
                        <li class="paginate_button page-item last" th:classappend="${users.last} ? 'disabled' : ''">
                            <a href="#"
                               class="page-link"
                               th:hx-get="@{/admin/companies/{id}/users(id=${companyId}, page=${users.totalPages}, size=10)}"
                               hx-target="#companyUsersList"
                               hx-swap="innerHTML"
                               hx-indicator="#modalHeaderSpinner"
                               hx-headers='{"HX-Request": "true"}'>
                                <i class="ti ti-chevrons-right ti-xs"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Available Users Modal -->
    <div class="modal fade" id="availableUsersModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex align-items-center">
                        <h5 class="modal-title me-2">Add Users to Company</h5>
                        <div id="modalHeaderSpinner" class="spinner-border spinner-border-sm text-primary" role="status" style="display: none;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded via HTMX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" 
                            class="btn btn-primary" 
                            hx-post="/admin/companies/users/add"
                            hx-include="[name='companyId'], [name='usersToAdd']"
                            hx-target="#companyUsersList"
                            hx-vals='{"page": "1", "size": "10"}'
                            hx-indicator="#modalHeaderSpinner"
                            id="addSelectedUsersButton">
                        Add Selected
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for handling user selection and modal closing -->
    <script>
        function toggleUserSelection(checkbox) {
            const selectedCount = document.querySelectorAll('.user-select-checkbox:checked').length;
            const actionsDiv = document.getElementById('selectedUsersActions');
            const countSpan = document.getElementById('selectedUsersCount');

            if (selectedCount > 0) {
                actionsDiv.classList.remove('d-none');
                countSpan.textContent = selectedCount;
            } else {
                actionsDiv.classList.add('d-none');
            }
        }

        // Function to clear the search input and trigger a new search
        function clearUserSearch() {
            const searchInput = document.getElementById('userSearchInput');
            if (searchInput) {
                searchInput.value = '';

                // Trigger a new search with empty search term
                const companyId = document.querySelector('[name="companyId"]').value;
                htmx.ajax('POST', '/admin/companies/users/search', {
                    target: '#companyUsersList',
                    swap: 'innerHTML',
                    values: {
                        companyId: companyId,
                        userSearchInput: '',
                        page: 1,
                        size: 10
                    },
                    indicator: '#modalHeaderSpinner'
                });
            }
        }

        // Add event listener for the add users button
        document.addEventListener('htmx:afterRequest', function(event) {
            if (event.target && event.target.id === 'addSelectedUsersButton') {
                if (event.detail.xhr.status === 200) {
                    // Close the modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('availableUsersModal'));
                    if (modal) {
                        modal.hide();
                    }
                }
            }
        });
    </script>
</div>

</body>
</html>
