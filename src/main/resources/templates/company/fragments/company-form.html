<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Company Form Fragment</title>
</head>
<body>

<div th:fragment="companyForm">
    <!-- Update offcanvas title using HTMX -->
    <div hx-swap-oob="innerHTML:#companyFormOffcanvasLabel" th:text="${isEdit} ? #{company.form.title.edit} : #{company.form.title.create}"></div>

    <!-- Alert Box for General Errors -->
    <div id="companyFormAlert" class="alert alert-dismissible fade" role="alert" style="display: none;">
        <div class="d-flex align-items-center">
            <i id="companyFormAlertIcon" class="ti ti-alert-circle me-2"></i>
            <div>
                <strong id="companyFormAlertTitle" th:text="#{alert.error.title}">Error</strong>
                <div id="companyFormAlertMessage" th:text="#{alert.error.unknown}">An unknown error occurred</div>
            </div>
        </div>
        <button type="button" class="btn-close" onclick="hideCompanyFormAlert()" th:attr="aria-label=#{common.close}"></button>
    </div>
    <form id="companyFormElement"
          th:hx-post="${isEdit} ? '/admin/companies/update/' + ${companyDto.id} : '/admin/companies/create'"
          hx-swap="none"
          hx-trigger="submit"
          class="needs-validation"
          novalidate>

                <!-- Company Name -->
                <div class="mb-3">
                    <label for="companyName" class="form-label" th:text="#{company.field.companyName}">Company Name</label>
                    <input type="text"
                           id="companyName"
                           name="companyName"
                           class="form-control"
                           th:value="${companyDto.companyName}"
                           required
                           maxlength="100"
                           th:placeholder="#{company.field.companyName}">
                    <div class="invalid-feedback"></div>
                </div>

                <!-- Country -->
                <div class="mb-3">
                    <label for="country" class="form-label" th:text="#{company.field.country}">Country</label>
                    <select id="country"
                            name="country"
                            class="form-select"
                            th:data-placeholder="#{company.field.country.placeholder}"
                            required>
                        <option value="" th:text="#{company.field.country.select}">Select Country</option>
                        <option th:each="countryOption : ${allCountries}"
                                th:value="${countryOption}"
                                th:text="${countryOption}"
                                th:selected="${countryOption == companyDto.country}">Country</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>

                <!-- Address -->
                <div class="mb-3">
                    <label for="address" class="form-label" th:text="#{company.field.address}">Address</label>
                    <textarea id="address"
                              name="address"
                              class="form-control"
                              rows="3"
                              th:text="${companyDto.address}"
                              required
                              maxlength="255"
                              th:placeholder="#{company.field.address}"></textarea>
                    <div class="invalid-feedback"></div>
                </div>

                <!-- TAX Number -->
                <div class="mb-3">
                    <label for="taxNumber" class="form-label" th:text="#{company.field.taxNumber}">TAX Number</label>
                    <input type="text"
                           id="taxNumber"
                           name="taxNumber"
                           class="form-control"
                           th:value="${companyDto.taxNumber}"
                           th:readonly="${isEdit}"
                           th:class="${isEdit} ? 'form-control-plaintext' : 'form-control'"
                           required
                           maxlength="50"
                           th:placeholder="#{company.field.taxNumber}">
                    <div class="invalid-feedback"></div>
                    <div class="form-text" th:if="${!isEdit}" th:text="#{company.field.taxNumber.help}">This must be unique for each company</div>
                    <div class="form-text" th:if="${isEdit}" th:text="#{company.field.taxNumber.readonly}">TAX number cannot be changed</div>
                </div>

                <!-- Domain Name -->
                <div class="mb-3">
                    <label for="domainName" class="form-label" th:text="#{company.field.domainName}">Domain Name</label>
                    <input type="text"
                           id="domainName"
                           name="domainName"
                           class="form-control"
                           th:value="${companyDto.domainName}"
                           maxlength="100"
                           th:placeholder="#{company.field.domainName}">
                    <div class="invalid-feedback"></div>
                    <div class="form-text" th:text="#{company.field.domainName.help}">Optional - company website domain</div>
                </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mt-4">
            <button type="button"
                    class="btn btn-outline-secondary"
                    data-bs-dismiss="offcanvas"
                    th:text="#{company.button.cancel}">
                Cancel
            </button>
            <button type="submit"
                    class="btn btn-primary"
                    th:text="#{company.button.save}">
                Save
            </button>
        </div>
    </form>
</div>

<style>
/* Alert Box Styles */
#companyFormAlert {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#companyFormAlert.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

#companyFormAlert.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #856404;
}

#companyFormAlert.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

#companyFormAlert.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

#companyFormAlert .btn-close {
    padding: 0.5rem;
    margin: -0.25rem -0.25rem -0.25rem auto;
}

#companyFormAlert i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

#companyFormAlertTitle {
    font-weight: 600;
    margin-bottom: 0.25rem;
    display: block;
}

#companyFormAlertMessage {
    font-size: 0.875rem;
    line-height: 1.4;
}
</style>

<script>
    // Add event listener for form submission response
    document.addEventListener('htmx:afterRequest', function(event) {
        const form = event.target;
        if (form && form.id === 'companyFormElement') {
            const xhr = event.detail.xhr;

            if (xhr.status === 200) {
                // Hide any existing alert
                if (typeof window.hideCompanyFormAlert === 'function') {
                    window.hideCompanyFormAlert();
                }

                // Close the offcanvas
                const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('companyFormOffcanvas'));
                if (offcanvas) {
                    offcanvas.hide();
                }

                // Refresh the company list
                if (typeof window.refreshCompanyListWithCurrentState === 'function') {
                    window.refreshCompanyListWithCurrentState();
                }
            } else {
                // Show error alert
                const responseText = xhr.responseText;
                try {
                    const responseObj = JSON.parse(responseText);
                    if (responseObj.title && responseObj.text) {
                        window.showCompanyFormAlert('error', responseObj.title, responseObj.text);
                    } else {
                        window.showCompanyFormAlert('error', 'Error', 'An error occurred while processing your request');
                    }
                } catch (e) {
                    window.showCompanyFormAlert('error', 'Error', 'An error occurred while processing your request');
                }
            }
        }
    });
</script>

</body>
</html>
