<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Available Users Fragment</title>
</head>
<body>

<div th:fragment="availableUsersList">
    <!-- Available Users Content -->
    <div class="available-users-container">
        <!-- Search Box -->
        <div class="mb-3">
            <div class="input-group input-group-merge">
                <span class="input-group-text"><i class="ti ti-search"></i></span>
                <input type="text" 
                       class="form-control" 
                       id="availableUserSearchInput"
                       name="searchInput"
                       placeholder="Search available users..." 
                       hx-get="/admin/companies/users/available"
                       hx-trigger="keyup changed delay:1000ms"
                       hx-target="#availableUsersModal .modal-body"
                       hx-include="[name='companyId'], [name='searchInput']"
                       hx-indicator="#modalHeaderSpinner">
                <button type="button" 
                        class="btn btn-outline-secondary input-group-text"
                        hx-get="/admin/companies/users/available"
                        hx-target="#availableUsersModal .modal-body"
                        hx-include="[name='companyId']"
                        hx-vals='{"page": "1", "size": "10", "searchInput": ""}'
                        hx-indicator="#modalHeaderSpinner"
                        onclick="document.getElementById('availableUserSearchInput').value = ''"
                        title="Clear filter">
                    <i class="ti ti-x"></i>
                </button>
            </div>
        </div>

        <!-- Hidden Company ID -->
        <input type="hidden" name="companyId" th:value="${companyId}">

        <!-- Available Users List -->
        <div th:if="${availableUsers == null || availableUsers.totalElements == 0}" class="text-center text-muted py-4">
            <i class="ti ti-users ti-lg mb-2"></i>
            <div>No available users found</div>
        </div>

        <div th:if="${availableUsers != null && availableUsers.totalElements > 0}" class="list-group mb-3">
            <div th:each="user : ${availableUsers.content}" class="list-group-item list-group-item-action d-flex align-items-center available-user-item">
                <div class="form-check me-3">
                    <input class="form-check-input" 
                           type="checkbox" 
                           th:value="${user.id}" 
                           name="usersToAdd">
                </div>
                <div class="d-flex align-items-center flex-grow-1">
                    <div class="avatar me-3">
                        <img th:if="${user.photo != null}" th:src="${'/images/' + user.photo}" class="rounded-circle" alt="User Photo">
                        <span th:if="${user.photo == null}" class="avatar-initial rounded-circle bg-label-primary">
                            <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                        </span>
                    </div>
                    <div class="d-flex flex-column">
                        <h6 class="mb-0 user-name" th:text="${user.fullName}">User Name</h6>
                        <small class="text-muted user-email" th:text="${user.email}"><EMAIL></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div th:if="${availableUsers != null && availableUsers.totalElements > 0}" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                <span>Showing from</span>
                <span th:text="${availableUsers.number * availableUsers.size + 1}">1</span>
                <span>to</span>
                <span th:text="${availableUsers.number * availableUsers.size + availableUsers.numberOfElements}">10</span>
                <span>of total</span>
                <span th:text="${availableUsers.totalElements}">100</span>
                <span>users</span>
            </div>

            <nav aria-label="Available users pagination" th:if="${availableUsers.totalPages > 1}">
                <ul class="pagination pagination-rounded pagination-sm mb-0">
                    <!-- First Page Button -->
                    <li class="paginate_button page-item first" th:classappend="${availableUsers.first} ? 'disabled' : ''">
                        <a href="#"
                           class="page-link"
                           th:hx-get="@{/admin/companies/users/available(companyId=${companyId}, page=1, size=10, searchInput=${searchTerm})}"
                           hx-target="#availableUsersModal .modal-body"
                           hx-swap="innerHTML"
                           hx-indicator="#modalHeaderSpinner">
                            <i class="ti ti-chevrons-left ti-xs"></i>
                        </a>
                    </li>

                    <!-- Previous Button -->
                    <li class="paginate_button page-item previous" th:classappend="${!availableUsers.hasPrevious()} ? 'disabled' : ''">
                        <a href="#"
                           class="page-link"
                           th:hx-get="@{/admin/companies/users/available(companyId=${companyId}, page=${availableUsers.number}, size=10, searchInput=${searchTerm})}"
                           hx-target="#availableUsersModal .modal-body"
                           hx-swap="innerHTML"
                           hx-indicator="#modalHeaderSpinner">
                            <i class="ti ti-chevron-left ti-xs"></i>
                        </a>
                    </li>

                    <!-- Page Numbers -->
                    <th:block th:if="${availableUsers.totalPages <= 5}">
                        <th:block th:each="i : ${#numbers.sequence(1, availableUsers.totalPages)}">
                            <li class="paginate_button page-item" th:classappend="${i == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${i}"
                                   th:hx-get="@{/admin/companies/users/available(companyId=${companyId}, page=${i}, size=10, searchInput=${searchTerm})}"
                                   hx-target="#availableUsersModal .modal-body"
                                   hx-swap="innerHTML"
                                   hx-indicator="#modalHeaderSpinner">
                                </a>
                            </li>
                        </th:block>
                    </th:block>

                    <!-- Next Button -->
                    <li class="paginate_button page-item next" th:classappend="${!availableUsers.hasNext()} ? 'disabled' : ''">
                        <a href="#"
                           class="page-link"
                           th:hx-get="@{/admin/companies/users/available(companyId=${companyId}, page=${availableUsers.number + 2}, size=10, searchInput=${searchTerm})}"
                           hx-target="#availableUsersModal .modal-body"
                           hx-swap="innerHTML"
                           hx-indicator="#modalHeaderSpinner">
                            <i class="ti ti-chevron-right ti-xs"></i>
                        </a>
                    </li>

                    <!-- Last Page Button -->
                    <li class="paginate_button page-item last" th:classappend="${availableUsers.last} ? 'disabled' : ''">
                        <a href="#"
                           class="page-link"
                           th:hx-get="@{/admin/companies/users/available(companyId=${companyId}, page=${availableUsers.totalPages}, size=10, searchInput=${searchTerm})}"
                           hx-target="#availableUsersModal .modal-body"
                           hx-swap="innerHTML"
                           hx-indicator="#modalHeaderSpinner">
                            <i class="ti ti-chevrons-right ti-xs"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Script removed in favor of HTMX attributes on the clear filter button -->

</body>
</html>
